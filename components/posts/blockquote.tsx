import * as React from "react"
import {cn} from "@/lib/utils"
import {AlertCircle, Info, CircleCheck, Bug, CircleQuestionMark} from "lucide-react";


export function Blockquote({
                          children,
                          className,
                          iconClassName,
                          type = "info",
                        }: {
  children: React.ReactNode
  className?: string
  iconClassName?: string
  type?: "info" | "question" | "success" | "warning" | "error"
}) {
  const styles = {
    info: "bg-blue-100 text-blue-900",
    question: "bg-gray-100 text-gray-900",
    success: "bg-green-100 text-green-900",
    warning: "bg-yellow-100 text-yellow-900",
    error: "bg-red-100 text-red-900",
  }

  const iconStyles = {
    info: "text-blue-600",
    question: "text-gray-600",
    success: "text-green-600",
    warning: "text-yellow-600",
    error: "text-red-600",
  }

  const icons = {
    info: Info,
    question: CircleQuestionMark,
    success: CircleCheck,
    warning: AlertCircle,
    error: Bug,
  }

  const IconComponent = icons[type]

  return (
    <div className={cn("w-full my-4 rounded-lg border-2 p-4 border-primary", styles[type], className)}>
      <div className="inline-flex align-middle">
        <IconComponent className={cn("w-5 h-5 mr-2 align-middle", iconStyles[type], iconClassName)} />
        <div>{children}</div>
      </div>
    </div>
  )
}
